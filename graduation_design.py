#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تصميم تحفة فنية رقمية لتخرج د.محمد صادق
Digital Art Masterpiece for Dr<PERSON> <PERSON>'s Graduation
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import arabic_reshaper
from bidi.algorithm import get_display
import os
import sys

def load_and_prepare_image(image_path):
    """تحميل وتحضير الصورة الأساسية"""
    try:
        # تحميل الصورة باستخدام PIL
        img = Image.open(image_path)
        
        # تحويل إلى RGB إذا لزم الأمر
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # تحسين جودة الصورة
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.2)
        
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.1)
        
        return img
    except Exception as e:
        print(f"خطأ في تحميل الصورة: {e}")
        return None

def create_golden_gradient_background(width, height):
    """إنشاء خلفية متدرجة ذهبية فاخرة"""
    # إنشاء صورة جديدة
    gradient = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(gradient)
    
    # ألوان التدرج الذهبي الفاخر
    colors = [
        (25, 25, 60, 180),      # أزرق كحلي داكن
        (40, 35, 80, 160),      # أزرق كحلي متوسط
        (184, 134, 11, 140),    # ذهبي داكن
        (255, 215, 0, 120),     # ذهبي لامع
        (255, 248, 220, 100),   # كريمي ذهبي
    ]
    
    # رسم التدرج
    for i in range(height):
        ratio = i / height
        if ratio < 0.3:
            # الجزء العلوي - أزرق كحلي
            color_ratio = ratio / 0.3
            r = int(colors[0][0] + (colors[1][0] - colors[0][0]) * color_ratio)
            g = int(colors[0][1] + (colors[1][1] - colors[0][1]) * color_ratio)
            b = int(colors[0][2] + (colors[1][2] - colors[0][2]) * color_ratio)
            a = int(colors[0][3] + (colors[1][3] - colors[0][3]) * color_ratio)
        elif ratio < 0.7:
            # الجزء الأوسط - انتقال للذهبي
            color_ratio = (ratio - 0.3) / 0.4
            r = int(colors[1][0] + (colors[2][0] - colors[1][0]) * color_ratio)
            g = int(colors[1][1] + (colors[2][1] - colors[1][1]) * color_ratio)
            b = int(colors[1][2] + (colors[2][2] - colors[1][2]) * color_ratio)
            a = int(colors[1][3] + (colors[2][3] - colors[1][3]) * color_ratio)
        else:
            # الجزء السفلي - ذهبي لامع
            color_ratio = (ratio - 0.7) / 0.3
            r = int(colors[2][0] + (colors[3][0] - colors[2][0]) * color_ratio)
            g = int(colors[2][1] + (colors[3][1] - colors[2][1]) * color_ratio)
            b = int(colors[2][2] + (colors[3][2] - colors[2][2]) * color_ratio)
            a = int(colors[2][3] + (colors[3][3] - colors[2][3]) * color_ratio)
        
        draw.line([(0, i), (width, i)], fill=(r, g, b, a))
    
    return gradient

def add_decorative_elements(img):
    """إضافة العناصر الزخرفية والفنية"""
    width, height = img.size
    overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(overlay)

    # إضافة زخارف إسلامية في الزوايا
    corner_size = min(width, height) // 8

    # زخرفة الزاوية العلوية اليمنى
    for i in range(8):
        radius = corner_size - i * 12
        if radius > 0:
            alpha = 100 - i * 12
            color = (255, 215, 0, alpha)  # ذهبي متدرج
            draw.arc([width - corner_size - 20, 20, width - 20, corner_size + 20],
                    start=180, end=270, fill=color, width=4)

    # زخرفة الزاوية السفلية اليسرى
    for i in range(8):
        radius = corner_size - i * 12
        if radius > 0:
            alpha = 100 - i * 12
            color = (255, 215, 0, alpha)
            draw.arc([20, height - corner_size - 20, corner_size + 20, height - 20],
                    start=0, end=90, fill=color, width=4)

    # إضافة نجوم متلألئة بأحجام مختلفة
    star_positions = [
        (width * 0.15, height * 0.2, 12),
        (width * 0.85, height * 0.3, 10),
        (width * 0.1, height * 0.7, 8),
        (width * 0.9, height * 0.8, 14),
        (width * 0.2, height * 0.9, 6),
        (width * 0.8, height * 0.1, 9),
        (width * 0.05, height * 0.5, 7),
        (width * 0.95, height * 0.6, 11),
    ]

    for x, y, size in star_positions:
        draw_star(draw, x, y, size, (255, 215, 0, 150))

    # إضافة خطوط زخرفية ذهبية
    add_decorative_lines(draw, width, height)

    return overlay

def add_decorative_lines(draw, width, height):
    """إضافة خطوط زخرفية ذهبية"""
    # خطوط زخرفية في الأعلى
    for i in range(3):
        y = height * 0.05 + i * 5
        alpha = 120 - i * 30
        draw.line([(width * 0.3, y), (width * 0.7, y)],
                 fill=(255, 215, 0, alpha), width=2)

    # خطوط زخرفية في الأسفل
    for i in range(3):
        y = height * 0.95 - i * 5
        alpha = 120 - i * 30
        draw.line([(width * 0.25, y), (width * 0.75, y)],
                 fill=(255, 215, 0, alpha), width=2)

def draw_star(draw, x, y, size, color):
    """رسم نجمة متلألئة"""
    points = []
    for i in range(10):
        angle = i * 36 * 3.14159 / 180
        if i % 2 == 0:
            radius = size
        else:
            radius = size * 0.4
        px = x + radius * np.cos(angle)
        py = y + radius * np.sin(angle)
        points.append((px, py))
    
    draw.polygon(points, fill=color)

def prepare_arabic_text(text):
    """تحضير النص العربي للعرض الصحيح"""
    try:
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except:
        return text

def add_main_text(img):
    """إضافة النص الرئيسي بتصميم فني مذهل"""
    width, height = img.size

    # إنشاء طبقة للنص
    text_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(text_layer)

    # النص الرئيسي
    main_text = "موعدنا اليوم 2025/7/28 في قاعة مملكة سبأ"
    highlight_text = "د.محمد صادق"
    subtitle_text = "لحفل تخرج حبيب القلب"

    # تحضير النصوص العربية
    main_text_display = prepare_arabic_text(main_text)
    highlight_text_display = prepare_arabic_text(highlight_text)
    subtitle_text_display = prepare_arabic_text(subtitle_text)

    # محاولة تحميل خط عربي، أو استخدام الافتراضي
    try:
        # يمكن تحميل خط عربي من Google Fonts أو استخدام خط النظام
        main_font = ImageFont.truetype("arial.ttf", int(width * 0.025))
        highlight_font = ImageFont.truetype("arial.ttf", int(width * 0.04))
        subtitle_font = ImageFont.truetype("arial.ttf", int(width * 0.018))
    except:
        # استخدام الخط الافتراضي إذا لم يتم العثور على الخط المطلوب
        main_font = ImageFont.load_default()
        highlight_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
    
    # حساب مواضع النص
    text_y_start = height * 0.72

    # إضافة إطار زخرفي للنص
    frame_padding = 40
    frame_x1 = width * 0.1
    frame_y1 = text_y_start - frame_padding
    frame_x2 = width * 0.9
    frame_y2 = text_y_start + 200

    # رسم الإطار الزخرفي
    for i in range(5):
        alpha = 60 - i * 10
        draw.rectangle([frame_x1 - i*2, frame_y1 - i*2, frame_x2 + i*2, frame_y2 + i*2],
                      outline=(255, 215, 0, alpha), width=2)

    # رسم النص الرئيسي مع تأثير الظل والتوهج
    main_bbox = draw.textbbox((0, 0), main_text_display, font=main_font)
    main_width = main_bbox[2] - main_bbox[0]
    main_x = (width - main_width) // 2

    # تأثير التوهج الذهبي المحسن
    for offset in range(8, 0, -1):
        alpha = 25 + offset * 8
        glow_color = (255, 215, 0, alpha)
        for dx in range(-offset, offset + 1):
            for dy in range(-offset, offset + 1):
                if dx*dx + dy*dy <= offset*offset:
                    draw.text((main_x + dx, text_y_start + dy), main_text_display,
                             font=main_font, fill=glow_color)

    # النص الأساسي مع حدود ذهبية
    draw.text((main_x, text_y_start), main_text_display,
              font=main_font, fill=(255, 255, 255, 255), stroke_width=2, stroke_fill=(255, 215, 0, 200))
    
    # النص المميز (اسم الدكتور) - النجم المتلألئ
    highlight_bbox = draw.textbbox((0, 0), highlight_text_display, font=highlight_font)
    highlight_width = highlight_bbox[2] - highlight_bbox[0]
    highlight_x = (width - highlight_width) // 2
    highlight_y = text_y_start + 70

    # إضافة خلفية مضيئة خاصة للاسم
    name_bg_x1 = highlight_x - 30
    name_bg_y1 = highlight_y - 15
    name_bg_x2 = highlight_x + highlight_width + 30
    name_bg_y2 = highlight_y + 80

    # رسم هالة ضوئية خلف الاسم
    for i in range(15, 0, -1):
        alpha = 15 + i * 3
        glow_color = (255, 215, 0, alpha)
        draw.ellipse([name_bg_x1 - i*3, name_bg_y1 - i*2, name_bg_x2 + i*3, name_bg_y2 + i*2],
                    fill=glow_color)

    # تأثير التوهج الذهبي المكثف للاسم
    for offset in range(12, 0, -1):
        alpha = 35 + offset * 6
        glow_color = (255, 215, 0, alpha)
        for dx in range(-offset, offset + 1):
            for dy in range(-offset, offset + 1):
                if dx*dx + dy*dy <= offset*offset:
                    draw.text((highlight_x + dx, highlight_y + dy), highlight_text_display,
                             font=highlight_font, fill=glow_color)

    # النص المميز بالذهبي اللامع مع حدود
    draw.text((highlight_x, highlight_y), highlight_text_display,
              font=highlight_font, fill=(255, 215, 0, 255), stroke_width=3, stroke_fill=(25, 25, 60, 255))
    
    # النص الفرعي
    subtitle_bbox = draw.textbbox((0, 0), subtitle_text_display, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    subtitle_y = highlight_y + 90
    
    # تأثير التوهج للنص الفرعي
    for offset in range(3, 0, -1):
        alpha = 40 + offset * 15
        glow_color = (200, 200, 255, alpha)
        for dx in range(-offset, offset + 1):
            for dy in range(-offset, offset + 1):
                if dx*dx + dy*dy <= offset*offset:
                    draw.text((subtitle_x + dx, subtitle_y + dy), subtitle_text_display, 
                             font=subtitle_font, fill=glow_color)
    
    draw.text((subtitle_x, subtitle_y), subtitle_text_display, 
              font=subtitle_font, fill=(255, 255, 255, 255))
    
    return text_layer

def add_graduation_symbols(img):
    """إضافة رموز التخرج الأكاديمية"""
    width, height = img.size
    symbols_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(symbols_layer)
    
    # رسم قبعة التخرج بشكل فني
    cap_x = width * 0.1
    cap_y = height * 0.15
    cap_size = 60
    
    # قاعدة القبعة
    draw.ellipse([cap_x - cap_size//2, cap_y, cap_x + cap_size//2, cap_y + cap_size//4], 
                 fill=(25, 25, 60, 150), outline=(255, 215, 0, 200), width=2)
    
    # الجزء العلوي من القبعة
    draw.rectangle([cap_x - cap_size//3, cap_y - cap_size//3, cap_x + cap_size//3, cap_y], 
                   fill=(25, 25, 60, 150), outline=(255, 215, 0, 200), width=2)
    
    # الشرابة
    draw.line([cap_x + cap_size//4, cap_y - cap_size//6, cap_x + cap_size//2, cap_y + cap_size//6], 
              fill=(255, 215, 0, 200), width=3)
    
    return symbols_layer

def create_masterpiece(image_path, output_path):
    """إنشاء التحفة الفنية الكاملة"""
    print("🎨 بدء إنشاء التحفة الفنية الرقمية...")
    
    # تحميل الصورة الأساسية
    base_img = load_and_prepare_image(image_path)
    if base_img is None:
        print("❌ فشل في تحميل الصورة")
        return False
    
    width, height = base_img.size
    print(f"📐 أبعاد الصورة: {width}x{height}")
    
    # إنشاء الخلفية المتدرجة
    print("🌅 إنشاء الخلفية الذهبية الفاخرة...")
    gradient_bg = create_golden_gradient_background(width, height)
    
    # دمج الصورة الأساسية مع الخلفية
    result = Image.alpha_composite(base_img.convert('RGBA'), gradient_bg)
    
    # إضافة العناصر الزخرفية
    print("✨ إضافة العناصر الزخرفية...")
    decorative_layer = add_decorative_elements(result)
    result = Image.alpha_composite(result, decorative_layer)
    
    # إضافة رموز التخرج
    print("🎓 إضافة رموز التخرج الأكاديمية...")
    symbols_layer = add_graduation_symbols(result)
    result = Image.alpha_composite(result, symbols_layer)
    
    # إضافة النص الرئيسي
    print("📝 إضافة النص الفني المذهل...")
    text_layer = add_main_text(result)
    result = Image.alpha_composite(result, text_layer)
    
    # تطبيق تأثيرات نهائية
    print("🎭 تطبيق اللمسات الأخيرة...")
    
    # تحسين الألوان والتباين
    enhancer = ImageEnhance.Color(result)
    result = enhancer.enhance(1.2)
    
    enhancer = ImageEnhance.Contrast(result)
    result = enhancer.enhance(1.1)
    
    # حفظ النتيجة النهائية
    result_rgb = result.convert('RGB')
    result_rgb.save(output_path, 'PNG', quality=95, optimize=True)
    
    print(f"✅ تم إنشاء التحفة الفنية بنجاح: {output_path}")
    return True

if __name__ == "__main__":
    # مسارات الملفات
    input_image = "WhatsApp Image 2025-07-28 at 03.29.09_612d4d3e (1).png"
    output_image = "graduation_masterpiece_dr_mohamed_sadiq.png"
    
    # التحقق من وجود الصورة الأساسية
    if not os.path.exists(input_image):
        print(f"❌ لم يتم العثور على الصورة: {input_image}")
        sys.exit(1)
    
    # إنشاء التحفة الفنية
    success = create_masterpiece(input_image, output_image)
    
    if success:
        print("\n🎉 تم إنشاء تحفة فنية رقمية مذهلة لتخرج د.محمد صادق!")
        print(f"📁 الملف محفوظ باسم: {output_image}")
        print("🌟 التصميم يتضمن:")
        print("   • خلفية ذهبية فاخرة متدرجة")
        print("   • نص عربي فني مع تأثيرات التوهج")
        print("   • زخارف إسلامية راقية")
        print("   • رموز أكاديمية للتخرج")
        print("   • نجوم متلألئة احتفالية")
        print("   • تأثيرات بصرية مذهلة")
    else:
        print("❌ فشل في إنشاء التصميم")
        sys.exit(1)
