#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎊 دعوة التخرج الأسطورية - أقوى تصميم في التاريخ
Ultimate Graduation Invitation Card - The Most Epic Design Ever Created
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from PIL.ImageFilter import GaussianBlur, UnsharpMask, BLUR, SMOOTH
import arabic_reshaper
from bidi.algorithm import get_display
import math
import os
import sys
import random
from scipy import ndimage
from scipy.ndimage import gaussian_filter
import colorsys

class UltimateInvitationDesigner:
    def __init__(self, image_path):
        self.image_path = image_path
        self.base_img = None
        
        # أبعاد البطاقة الفاخرة (نسبة ذهبية)
        self.card_width = 1920   # عرض فاخر
        self.card_height = 1200  # ارتفاع مثالي
        
        # ألوان فاخرة حصرية
        self.luxury_colors = {
            'royal_gold': (255, 215, 0),
            'platinum': (229, 228, 226),
            'diamond_white': (248, 248, 255),
            'royal_purple': (102, 51, 153),
            'emerald': (80, 200, 120),
            'sapphire': (15, 82, 186),
            'ruby': (224, 17, 95),
            'cosmic_blue': (72, 61, 139),
            'sunset_gold': (255, 165, 0),
            'pearl_white': (255, 255, 240)
        }
        
    def load_and_ultimate_enhance(self):
        """تحميل وتحسين الصورة بأقوى التقنيات"""
        try:
            print("🚀 تطبيق تحسينات الذكاء الاصطناعي الفائقة...")
            
            # تحميل الصورة
            img_cv = cv2.imread(self.image_path)
            
            # تطبيق أقوى فلاتر الذكاء الاصطناعي
            # 1. تحسين التفاصيل بالذكاء الاصطناعي
            img_cv = cv2.detailEnhance(img_cv, sigma_s=10, sigma_r=0.15)
            
            # 2. تحسين الحواف مع الحفاظ على النعومة
            img_cv = cv2.edgePreservingFilter(img_cv, flags=1, sigma_s=50, sigma_r=0.4)
            
            # 3. تطبيق stylization فني
            img_cv = cv2.stylization(img_cv, sigma_s=150, sigma_r=0.25)
            
            # 4. تحسين الألوان بتقنية CLAHE
            lab = cv2.cvtColor(img_cv, cv2.COLOR_BGR2LAB)
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            lab[:,:,0] = clahe.apply(lab[:,:,0])
            img_cv = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            
            # تحويل إلى PIL
            img_rgb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)
            self.base_img = Image.fromarray(img_rgb).convert('RGBA')
            
            # تحسينات PIL متقدمة
            enhancer = ImageEnhance.Sharpness(self.base_img)
            self.base_img = enhancer.enhance(2.2)
            
            enhancer = ImageEnhance.Contrast(self.base_img)
            self.base_img = enhancer.enhance(1.8)
            
            enhancer = ImageEnhance.Color(self.base_img)
            self.base_img = enhancer.enhance(1.7)
            
            enhancer = ImageEnhance.Brightness(self.base_img)
            self.base_img = enhancer.enhance(1.1)
            
            print(f"✅ تم تحسين الصورة بأقوى التقنيات!")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحسين الصورة: {e}")
            return False
    
    def create_luxury_card_background(self):
        """إنشاء خلفية البطاقة الفاخرة الأسطورية"""
        print("💎 إنشاء خلفية البطاقة الفاخرة...")
        
        # إنشاء البطاقة الأساسية
        card = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 255))
        
        # طبقات الخلفية الفاخرة
        luxury_layers = [
            self.create_cosmic_gradient(),
            self.create_diamond_texture(),
            self.create_royal_patterns(),
            self.create_golden_rays(),
            self.create_pearl_shimmer(),
            self.create_luxury_borders(),
            self.create_floating_gems(),
        ]
        
        # دمج الطبقات بتقنيات متقدمة
        for i, layer in enumerate(luxury_layers):
            if i == 0:
                card = Image.alpha_composite(card, layer)
            elif i == 1:
                card = self.blend_luxury(card, layer, 'multiply')
            elif i == 2:
                card = self.blend_luxury(card, layer, 'overlay')
            elif i == 3:
                card = self.blend_luxury(card, layer, 'screen')
            elif i == 4:
                card = Image.alpha_composite(card, layer)
            else:
                card = self.blend_luxury(card, layer, 'soft_light')
        
        return card
    
    def create_cosmic_gradient(self):
        """إنشاء تدرج كوني فاخر"""
        gradient = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        gradient_array = np.zeros((self.card_height, self.card_width, 4), dtype=np.float32)
        
        # ألوان كونية فاخرة
        cosmic_colors = [
            (5, 5, 15, 255),          # أسود كوني عميق
            (15, 10, 35, 250),        # أزرق كوني داكن
            (25, 15, 60, 240),        # أزرق كوني
            (40, 25, 90, 230),        # بنفسجي كوني
            (60, 35, 120, 220),       # بنفسجي ملكي
            (90, 50, 150, 210),       # بنفسجي فاخر
            (120, 70, 180, 200),      # بنفسجي لامع
            (150, 90, 210, 190),      # بنفسجي ذهبي
            (180, 120, 240, 180),     # بنفسجي فاتح
            (210, 150, 255, 170),     # بنفسجي سماوي
            (240, 180, 100, 160),     # ذهبي كوني
            (255, 215, 0, 150),       # ذهبي ملكي
            (255, 235, 50, 140),      # ذهبي لامع
            (255, 245, 150, 130),     # ذهبي فاتح
            (255, 250, 200, 120),     # ذهبي شفاف
            (255, 255, 255, 110),     # أبيض كوني
        ]
        
        center_x, center_y = self.card_width // 2, self.card_height // 2
        max_distance = math.sqrt(center_x**2 + center_y**2)
        
        for y in range(self.card_height):
            for x in range(self.card_width):
                # حساب المسافة من المركز
                distance = math.sqrt((x - center_x)**2 + (y - center_y)**2)
                
                # تطبيق موجات كونية معقدة
                wave1 = math.sin(distance * 0.015 + x * 0.008) * 0.4
                wave2 = math.cos(distance * 0.012 + y * 0.006) * 0.3
                wave3 = math.sin((x + y) * 0.004) * 0.2
                wave4 = math.cos((x - y) * 0.003) * 0.1
                
                # تأثير دوامة كونية
                angle = math.atan2(y - center_y, x - center_x)
                spiral = math.sin(angle * 3 + distance * 0.01) * 0.2
                
                # حساب النسبة النهائية
                ratio = (distance / max_distance) + wave1 + wave2 + wave3 + wave4 + spiral
                ratio = max(0, min(1, ratio))
                
                # تحديد اللون
                color_index = ratio * (len(cosmic_colors) - 1)
                idx1 = int(color_index)
                idx2 = min(idx1 + 1, len(cosmic_colors) - 1)
                local_ratio = color_index - idx1
                
                # خلط الألوان
                c1 = cosmic_colors[idx1]
                c2 = cosmic_colors[idx2]
                
                r = c1[0] + (c2[0] - c1[0]) * local_ratio
                g = c1[1] + (c2[1] - c1[1]) * local_ratio
                b = c1[2] + (c2[2] - c1[2]) * local_ratio
                a = c1[3] + (c2[3] - c1[3]) * local_ratio
                
                gradient_array[y, x] = [r, g, b, a]
        
        return Image.fromarray(gradient_array.astype(np.uint8), 'RGBA')
    
    def create_diamond_texture(self):
        """إنشاء نسيج الماس الفاخر"""
        texture = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        texture_array = np.zeros((self.card_height, self.card_width, 4), dtype=np.uint8)
        
        # نمط الماس المعقد
        for y in range(self.card_height):
            for x in range(self.card_width):
                # حساب نمط الماس
                diamond_x = abs((x % 60) - 30) / 30.0
                diamond_y = abs((y % 60) - 30) / 30.0
                diamond_pattern = 1.0 - max(diamond_x, diamond_y)
                
                # تأثير التلألؤ
                shimmer = math.sin(x * 0.1) * math.cos(y * 0.1) * 0.5 + 0.5
                
                # تأثير الانكسار
                refraction = math.sin((x + y) * 0.05) * 0.3 + 0.7
                
                # حساب الشدة النهائية
                intensity = diamond_pattern * shimmer * refraction
                
                if intensity > 0.3:
                    alpha = int(intensity * 80)
                    # ألوان الماس
                    if intensity > 0.8:
                        color = (255, 255, 255, alpha)  # أبيض ماسي
                    elif intensity > 0.6:
                        color = (240, 240, 255, alpha)  # أزرق ماسي
                    elif intensity > 0.4:
                        color = (255, 240, 240, alpha)  # وردي ماسي
                    else:
                        color = (240, 255, 240, alpha)  # أخضر ماسي
                    
                    texture_array[y, x] = color
        
        return Image.fromarray(texture_array, 'RGBA').filter(GaussianBlur(radius=1))
    
    def create_royal_patterns(self):
        """إنشاء أنماط ملكية فاخرة"""
        patterns = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(patterns)
        
        # أنماط إسلامية ملكية
        self.draw_islamic_geometry(draw)
        self.draw_royal_medallions(draw)
        self.draw_luxury_arabesques(draw)
        
        return patterns.filter(GaussianBlur(radius=2))
    
    def draw_islamic_geometry(self, draw):
        """رسم الهندسة الإسلامية الفاخرة"""
        center_x, center_y = self.card_width // 2, self.card_height // 2
        
        # نجمة إسلامية مركزية فاخرة
        for radius in range(80, 200, 20):
            # نجمة ثمانية
            points = []
            for i in range(16):
                angle = i * math.pi / 8
                if i % 2 == 0:
                    r = radius
                else:
                    r = radius * 0.6
                
                x = center_x + r * math.cos(angle)
                y = center_y + r * math.sin(angle)
                points.append((x, y))
            
            # رسم النجمة مع تأثير ذهبي
            for thickness in range(8, 0, -1):
                alpha = 60 - thickness * 6
                if radius > 150:
                    color = (*self.luxury_colors['royal_gold'], alpha)
                elif radius > 120:
                    color = (*self.luxury_colors['sunset_gold'], alpha)
                else:
                    color = (*self.luxury_colors['platinum'], alpha)
                
                if len(points) > 2:
                    draw.polygon(points, outline=color, width=thickness)
        
        # أنماط هندسية متكررة
        for offset_x in [-400, 0, 400]:
            for offset_y in [-300, 0, 300]:
                if offset_x == 0 and offset_y == 0:
                    continue
                
                pattern_x = center_x + offset_x
                pattern_y = center_y + offset_y
                
                if 0 <= pattern_x <= self.card_width and 0 <= pattern_y <= self.card_height:
                    self.draw_small_geometric_pattern(draw, pattern_x, pattern_y)
    
    def draw_small_geometric_pattern(self, draw, cx, cy):
        """رسم نمط هندسي صغير"""
        for radius in range(20, 60, 15):
            # مضلع سداسي
            points = []
            for i in range(6):
                angle = i * math.pi / 3
                x = cx + radius * math.cos(angle)
                y = cy + radius * math.sin(angle)
                points.append((x, y))
            
            alpha = 40 - radius // 3
            color = (*self.luxury_colors['emerald'], alpha)
            
            if len(points) > 2:
                draw.polygon(points, outline=color, width=2)
    
    def draw_royal_medallions(self, draw):
        """رسم ميداليات ملكية"""
        medallion_positions = [
            (self.card_width * 0.2, self.card_height * 0.2),
            (self.card_width * 0.8, self.card_height * 0.2),
            (self.card_width * 0.2, self.card_height * 0.8),
            (self.card_width * 0.8, self.card_height * 0.8),
        ]
        
        for mx, my in medallion_positions:
            # ميدالية دائرية فاخرة
            for radius in range(50, 10, -5):
                alpha = 80 - radius
                if radius > 35:
                    color = (*self.luxury_colors['royal_purple'], alpha)
                elif radius > 25:
                    color = (*self.luxury_colors['sapphire'], alpha)
                else:
                    color = (*self.luxury_colors['royal_gold'], alpha)
                
                draw.ellipse([mx - radius, my - radius, mx + radius, my + radius], 
                           outline=color, width=3)
            
            # نجمة مركزية في الميدالية
            star_points = []
            for i in range(8):
                angle = i * math.pi / 4
                if i % 2 == 0:
                    r = 15
                else:
                    r = 8
                
                x = mx + r * math.cos(angle)
                y = my + r * math.sin(angle)
                star_points.append((x, y))
            
            if len(star_points) > 2:
                draw.polygon(star_points, fill=(*self.luxury_colors['royal_gold'], 120))
    
    def draw_luxury_arabesques(self, draw):
        """رسم زخارف عربية فاخرة"""
        # زخارف في الزوايا
        corners = [
            (50, 50, 1),      # أعلى يسار
            (self.card_width - 50, 50, -1),     # أعلى يمين
            (50, self.card_height - 50, -1),    # أسفل يسار
            (self.card_width - 50, self.card_height - 50, 1),  # أسفل يمين
        ]
        
        for cx, cy, direction in corners:
            # رسم زخرفة عربية معقدة
            for i in range(3):
                curve_radius = 30 + i * 15
                
                # منحنيات عربية
                for angle in range(0, 360, 30):
                    start_angle = math.radians(angle)
                    end_angle = math.radians(angle + 60)
                    
                    start_x = cx + curve_radius * math.cos(start_angle) * direction
                    start_y = cy + curve_radius * math.sin(start_angle)
                    end_x = cx + curve_radius * math.cos(end_angle) * direction
                    end_y = cy + curve_radius * math.sin(end_angle)
                    
                    alpha = 60 - i * 15
                    color = (*self.luxury_colors['emerald'], alpha)
                    
                    draw.line([(start_x, start_y), (end_x, end_y)], 
                             fill=color, width=3)
    
    def blend_luxury(self, base, overlay, mode):
        """دمج الطبقات بأنماط فاخرة"""
        if mode == 'multiply':
            base_array = np.array(base.convert('RGB'), dtype=np.float32) / 255.0
            overlay_array = np.array(overlay.convert('RGB'), dtype=np.float32) / 255.0
            result = base_array * overlay_array
            result = (result * 255).astype(np.uint8)
            return Image.fromarray(result).convert('RGBA')
        
        elif mode == 'screen':
            base_array = np.array(base.convert('RGB'), dtype=np.float32) / 255.0
            overlay_array = np.array(overlay.convert('RGB'), dtype=np.float32) / 255.0
            result = 1 - (1 - base_array) * (1 - overlay_array)
            result = (result * 255).astype(np.uint8)
            return Image.fromarray(result).convert('RGBA')
        
        elif mode == 'overlay':
            base_array = np.array(base.convert('RGB'), dtype=np.float32) / 255.0
            overlay_array = np.array(overlay.convert('RGB'), dtype=np.float32) / 255.0
            mask = base_array < 0.5
            result = np.where(mask, 
                             2 * base_array * overlay_array,
                             1 - 2 * (1 - base_array) * (1 - overlay_array))
            result = (result * 255).astype(np.uint8)
            return Image.fromarray(result).convert('RGBA')
        
        elif mode == 'soft_light':
            base_array = np.array(base.convert('RGB'), dtype=np.float32) / 255.0
            overlay_array = np.array(overlay.convert('RGB'), dtype=np.float32) / 255.0
            result = (1 - 2 * overlay_array) * base_array**2 + 2 * overlay_array * base_array
            result = np.clip(result, 0, 1)
            result = (result * 255).astype(np.uint8)
            return Image.fromarray(result).convert('RGBA')
        
        else:
            return Image.alpha_composite(base, overlay)

    def create_golden_rays(self):
        """إنشاء أشعة ذهبية فاخرة"""
        rays = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(rays)

        center_x, center_y = self.card_width // 2, self.card_height // 3

        # أشعة ذهبية متعددة الاتجاهات
        for angle in range(0, 360, 12):
            ray_length = 600
            end_x = center_x + ray_length * math.cos(math.radians(angle))
            end_y = center_y + ray_length * math.sin(math.radians(angle))

            # تدرج الأشعة الذهبية
            for thickness in range(15, 0, -1):
                alpha = 40 - thickness * 2
                if thickness > 10:
                    color = (*self.luxury_colors['royal_gold'], alpha)
                elif thickness > 5:
                    color = (*self.luxury_colors['sunset_gold'], alpha)
                else:
                    color = (*self.luxury_colors['pearl_white'], alpha)

                draw.line([(center_x, center_y), (end_x, end_y)],
                         fill=color, width=thickness)

        return rays.filter(GaussianBlur(radius=8))

    def create_pearl_shimmer(self):
        """إنشاء تلألؤ اللؤلؤ"""
        shimmer = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        shimmer_array = np.zeros((self.card_height, self.card_width, 4), dtype=np.uint8)

        # تأثير تلألؤ اللؤلؤ
        for y in range(self.card_height):
            for x in range(self.card_width):
                # موجات تلألؤ متعددة
                wave1 = math.sin(x * 0.02 + y * 0.01) * 0.5 + 0.5
                wave2 = math.cos(x * 0.015 + y * 0.02) * 0.3 + 0.7
                wave3 = math.sin((x + y) * 0.008) * 0.2 + 0.8

                # تأثير اللؤلؤ
                pearl_intensity = wave1 * wave2 * wave3

                if pearl_intensity > 0.7:
                    alpha = int((pearl_intensity - 0.7) * 200)

                    # ألوان اللؤلؤ المتدرجة
                    if pearl_intensity > 0.9:
                        color = (*self.luxury_colors['pearl_white'], alpha)
                    elif pearl_intensity > 0.85:
                        color = (*self.luxury_colors['diamond_white'], alpha)
                    elif pearl_intensity > 0.8:
                        color = (*self.luxury_colors['platinum'], alpha)
                    else:
                        color = (240, 240, 250, alpha)

                    shimmer_array[y, x] = color

        return Image.fromarray(shimmer_array, 'RGBA').filter(GaussianBlur(radius=3))

    def create_luxury_borders(self):
        """إنشاء حدود فاخرة للبطاقة"""
        borders = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(borders)

        # الحدود الخارجية الفاخرة
        border_thickness = 20

        # حدود متعددة الطبقات
        for i in range(border_thickness, 0, -2):
            alpha = 100 + i * 3

            if i > 15:
                color = (*self.luxury_colors['royal_gold'], alpha)
            elif i > 10:
                color = (*self.luxury_colors['platinum'], alpha)
            elif i > 5:
                color = (*self.luxury_colors['diamond_white'], alpha)
            else:
                color = (*self.luxury_colors['pearl_white'], alpha)

            # رسم الحدود
            draw.rectangle([i, i, self.card_width - i, self.card_height - i],
                          outline=color, width=2)

        # زخارف الزوايا الفاخرة
        corner_size = 80
        self.draw_luxury_corner_decorations(draw, corner_size)

        return borders

    def draw_luxury_corner_decorations(self, draw, size):
        """رسم زخارف الزوايا الفاخرة"""
        corners = [
            (size, size),                                           # أعلى يسار
            (self.card_width - size, size),                        # أعلى يمين
            (size, self.card_height - size),                       # أسفل يسار
            (self.card_width - size, self.card_height - size),     # أسفل يمين
        ]

        for cx, cy in corners:
            # زخرفة دائرية فاخرة
            for radius in range(size, 10, -5):
                alpha = 120 - radius

                if radius > size * 0.8:
                    color = (*self.luxury_colors['royal_gold'], alpha)
                elif radius > size * 0.6:
                    color = (*self.luxury_colors['emerald'], alpha)
                elif radius > size * 0.4:
                    color = (*self.luxury_colors['sapphire'], alpha)
                else:
                    color = (*self.luxury_colors['ruby'], alpha)

                draw.ellipse([cx - radius, cy - radius, cx + radius, cy + radius],
                           outline=color, width=3)

            # نجمة مركزية في الزخرفة
            star_size = size // 4
            star_points = []
            for i in range(8):
                angle = i * math.pi / 4
                if i % 2 == 0:
                    r = star_size
                else:
                    r = star_size * 0.5

                x = cx + r * math.cos(angle)
                y = cy + r * math.sin(angle)
                star_points.append((x, y))

            if len(star_points) > 2:
                draw.polygon(star_points, fill=(*self.luxury_colors['royal_gold'], 150))

    def create_floating_gems(self):
        """إنشاء أحجار كريمة عائمة"""
        gems = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(gems)

        # أحجار كريمة متنوعة
        gem_types = [
            {'color': self.luxury_colors['emerald'], 'count': 15, 'size_range': (8, 20)},
            {'color': self.luxury_colors['sapphire'], 'count': 12, 'size_range': (6, 16)},
            {'color': self.luxury_colors['ruby'], 'count': 10, 'size_range': (10, 22)},
            {'color': self.luxury_colors['royal_gold'], 'count': 8, 'size_range': (12, 25)},
        ]

        for gem_type in gem_types:
            for _ in range(gem_type['count']):
                x = random.randint(100, self.card_width - 100)
                y = random.randint(100, self.card_height - 100)
                size = random.randint(*gem_type['size_range'])

                self.draw_luxury_gem(draw, x, y, size, gem_type['color'])

        return gems.filter(GaussianBlur(radius=1))

    def draw_luxury_gem(self, draw, x, y, size, color):
        """رسم حجر كريم فاخر"""
        # هالة الحجر الكريم
        for i in range(size * 2, 0, -1):
            alpha = 60 - i * 2
            if alpha > 0:
                gem_color = (*color, alpha)
                draw.ellipse([x - i, y - i, x + i, y + i], fill=gem_color)

        # الحجر الكريم الأساسي
        # شكل ماسي
        diamond_points = []
        for i in range(6):
            angle = i * math.pi / 3
            radius = size if i % 2 == 0 else size * 0.7
            px = x + radius * math.cos(angle)
            py = y + radius * math.sin(angle)
            diamond_points.append((px, py))

        if len(diamond_points) > 2:
            # الحجر الأساسي
            draw.polygon(diamond_points, fill=(*color, 180))

            # بريق الحجر
            for i in range(3):
                inner_points = []
                for px, py in diamond_points:
                    inner_x = x + (px - x) * (0.8 - i * 0.2)
                    inner_y = y + (py - y) * (0.8 - i * 0.2)
                    inner_points.append((inner_x, inner_y))

                brightness = 255 - i * 50
                draw.polygon(inner_points, fill=(brightness, brightness, brightness, 120))

    def create_epic_text_effects(self, text, font_size, position, text_type='normal'):
        """إنشاء تأثيرات نص أسطورية"""
        text_layer = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(text_layer)

        # تحضير النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        display_text = get_display(reshaped_text)

        # تحميل الخط
        try:
            if text_type == 'title':
                font = ImageFont.truetype("arial.ttf", font_size)
            else:
                font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()

        # حساب موضع النص
        bbox = draw.textbbox((0, 0), display_text, font=font)
        text_width = bbox[2] - bbox[0]
        text_x = (self.card_width - text_width) // 2
        text_y = position

        if text_type == 'title':
            self.create_royal_title_effect(draw, display_text, font, text_x, text_y)
        elif text_type == 'highlight':
            self.create_diamond_highlight_effect(draw, display_text, font, text_x, text_y)
        else:
            self.create_elegant_text_effect(draw, display_text, font, text_x, text_y)

        return text_layer

    def create_royal_title_effect(self, draw, text, font, x, y):
        """تأثير العنوان الملكي"""
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # إنشاء مجال طاقة ملكي حول النص
        for i in range(80, 0, -1):
            alpha = 8 + i // 4
            energy_size = i * 6

            # ألوان الطاقة الملكية
            if i > 65:
                color = (*self.luxury_colors['diamond_white'], alpha)
            elif i > 50:
                color = (*self.luxury_colors['royal_gold'], alpha)
            elif i > 35:
                color = (*self.luxury_colors['sunset_gold'], alpha)
            elif i > 20:
                color = (*self.luxury_colors['platinum'], alpha)
            else:
                color = (*self.luxury_colors['pearl_white'], alpha)

            # رسم مجال الطاقة الملكي
            draw.ellipse([
                x - energy_size, y - energy_size//2,
                x + text_width + energy_size, y + text_height + energy_size//2
            ], fill=color)

        # رسم أشعة التاج الملكي
        crown_center_x = x + text_width // 2
        crown_center_y = y - 30

        for angle in range(0, 360, 20):
            ray_length = 120
            end_x = crown_center_x + ray_length * math.cos(math.radians(angle))
            end_y = crown_center_y + ray_length * math.sin(math.radians(angle))

            for thickness in range(10, 0, -1):
                alpha = 80 - thickness * 7
                ray_color = (*self.luxury_colors['royal_gold'], alpha)
                draw.line([(crown_center_x, crown_center_y), (end_x, end_y)],
                         fill=ray_color, width=thickness)

        # رسم ظلال ملكية متعددة الأبعاد
        shadow_layers = [
            (20, 20, (0, 0, 0, 200)),
            (16, 16, (20, 0, 40, 180)),
            (12, 12, (40, 0, 80, 160)),
            (8, 8, (60, 20, 120, 140)),
            (4, 4, (80, 40, 160, 120)),
        ]

        for dx, dy, color in shadow_layers:
            draw.text((x + dx, y + dy), text, font=font, fill=color)

        # رسم التوهج الملكي المتقدم
        for offset in range(50, 0, -1):
            alpha = 20 + offset * 2

            # تدرج ألوان ملكية
            if offset > 40:
                glow_color = (*self.luxury_colors['diamond_white'], alpha)
            elif offset > 30:
                glow_color = (*self.luxury_colors['pearl_white'], alpha)
            elif offset > 20:
                glow_color = (*self.luxury_colors['royal_gold'], alpha)
            elif offset > 10:
                glow_color = (*self.luxury_colors['sunset_gold'], alpha)
            else:
                glow_color = (*self.luxury_colors['platinum'], alpha)

            for dx in range(-offset, offset + 1, 3):
                for dy in range(-offset, offset + 1, 3):
                    if dx*dx + dy*dy <= offset*offset:
                        draw.text((x + dx, y + dy), text, font=font, fill=glow_color)

        # النص الأساسي مع تأثير التاج
        crown_layers = [
            (0, 0, (*self.luxury_colors['diamond_white'], 255)),
            (-2, -1, (*self.luxury_colors['pearl_white'], 240)),
            (-4, -2, (*self.luxury_colors['royal_gold'], 220)),
            (-6, -3, (*self.luxury_colors['sunset_gold'], 200)),
            (-8, -4, (*self.luxury_colors['platinum'], 180)),
        ]

        for dx, dy, color in crown_layers:
            draw.text((x + dx, y + dy), text, font=font, fill=color,
                     stroke_width=4, stroke_fill=(25, 25, 60, 255))

    def create_diamond_highlight_effect(self, draw, text, font, x, y):
        """تأثير الماس للنص المميز"""
        # رسم ظلال ماسية
        shadow_layers = [
            (15, 15, (0, 0, 0, 180)),
            (12, 12, (20, 0, 40, 160)),
            (9, 9, (40, 0, 80, 140)),
            (6, 6, (60, 20, 120, 120)),
            (3, 3, (80, 40, 160, 100)),
        ]

        for dx, dy, color in shadow_layers:
            draw.text((x + dx, y + dy), text, font=font, fill=color)

        # رسم التوهج الماسي
        for offset in range(35, 0, -1):
            alpha = 25 + offset * 3

            # ألوان ماسية متدرجة
            if offset > 28:
                glow_color = (*self.luxury_colors['diamond_white'], alpha)
            elif offset > 21:
                glow_color = (*self.luxury_colors['platinum'], alpha)
            elif offset > 14:
                glow_color = (*self.luxury_colors['pearl_white'], alpha)
            else:
                glow_color = (*self.luxury_colors['royal_gold'], alpha)

            for dx in range(-offset, offset + 1, 2):
                for dy in range(-offset, offset + 1, 2):
                    if dx*dx + dy*dy <= offset*offset:
                        draw.text((x + dx, y + dy), text, font=font, fill=glow_color)

        # النص الأساسي مع تأثير الماس
        diamond_layers = [
            (0, 0, (*self.luxury_colors['diamond_white'], 255)),
            (-1, 0, (*self.luxury_colors['platinum'], 240)),
            (-2, 0, (*self.luxury_colors['pearl_white'], 220)),
            (-3, 0, (*self.luxury_colors['royal_gold'], 200)),
        ]

        for dx, dy, color in diamond_layers:
            draw.text((x + dx, y + dy), text, font=font, fill=color,
                     stroke_width=3, stroke_fill=(50, 100, 150, 200))

    def create_elegant_text_effect(self, draw, text, font, x, y):
        """تأثير نص أنيق"""
        # رسم ظلال أنيقة
        shadow_layers = [
            (8, 8, (0, 0, 0, 150)),
            (6, 6, (0, 20, 40, 130)),
            (4, 4, (20, 40, 80, 110)),
            (2, 2, (40, 60, 120, 90)),
        ]

        for dx, dy, color in shadow_layers:
            draw.text((x + dx, y + dy), text, font=font, fill=color)

        # رسم التوهج الأنيق
        for offset in range(15, 0, -1):
            alpha = 30 + offset * 4

            # ألوان أنيقة متدرجة
            if offset > 10:
                glow_color = (*self.luxury_colors['emerald'], alpha)
            else:
                glow_color = (*self.luxury_colors['sapphire'], alpha)

            for dx in range(-offset, offset + 1, 2):
                for dy in range(-offset, offset + 1, 2):
                    if dx*dx + dy*dy <= offset*offset:
                        draw.text((x + dx, y + dy), text, font=font, fill=glow_color)

        # النص الأساسي مع تأثير أنيق
        elegant_layers = [
            (0, 0, (*self.luxury_colors['pearl_white'], 255)),
            (-1, 0, (*self.luxury_colors['platinum'], 230)),
            (-2, 0, (*self.luxury_colors['emerald'], 200)),
        ]

        for dx, dy, color in elegant_layers:
            draw.text((x + dx, y + dy), text, font=font, fill=color,
                     stroke_width=2, stroke_fill=(30, 80, 120, 180))

    def add_luxury_particle_system(self):
        """نظام جسيمات فاخر"""
        particles = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(particles)

        # أنواع الجسيمات الفاخرة
        luxury_particles = [
            {'count': 60, 'type': 'golden_star', 'size_range': (6, 18), 'colors': [self.luxury_colors['royal_gold'], self.luxury_colors['sunset_gold']]},
            {'count': 40, 'type': 'diamond_sparkle', 'size_range': (4, 12), 'colors': [self.luxury_colors['diamond_white'], self.luxury_colors['platinum']]},
            {'count': 30, 'type': 'emerald_glow', 'size_range': (8, 20), 'colors': [self.luxury_colors['emerald'], self.luxury_colors['sapphire']]},
            {'count': 25, 'type': 'pearl_shimmer', 'size_range': (5, 15), 'colors': [self.luxury_colors['pearl_white'], self.luxury_colors['platinum']]},
        ]

        for particle_group in luxury_particles:
            for _ in range(particle_group['count']):
                x = random.randint(0, self.card_width)
                y = random.randint(0, self.card_height)
                size = random.randint(*particle_group['size_range'])
                color = random.choice(particle_group['colors'])

                # تركيز أكثر حول مناطق النص
                if (self.card_height * 0.4 < y < self.card_height * 0.9):
                    size += 3
                    alpha_boost = 60
                else:
                    alpha_boost = 0

                # رسم الجسيمة حسب النوع
                if particle_group['type'] == 'golden_star':
                    self.draw_golden_star(draw, x, y, size, color, alpha_boost)
                elif particle_group['type'] == 'diamond_sparkle':
                    self.draw_diamond_sparkle(draw, x, y, size, color, alpha_boost)
                elif particle_group['type'] == 'emerald_glow':
                    self.draw_emerald_glow(draw, x, y, size, color, alpha_boost)
                elif particle_group['type'] == 'pearl_shimmer':
                    self.draw_pearl_shimmer_particle(draw, x, y, size, color, alpha_boost)

        return particles

    def draw_golden_star(self, draw, x, y, size, color, alpha_boost):
        """رسم نجمة ذهبية"""
        # نجمة خماسية
        star_points = []
        for i in range(10):
            angle = i * math.pi / 5
            if i % 2 == 0:
                r = size
            else:
                r = size * 0.4

            px = x + r * math.cos(angle)
            py = y + r * math.sin(angle)
            star_points.append((px, py))

        # رسم هالة النجمة
        for i in range(size * 2, 0, -1):
            alpha = 80 + alpha_boost - i * 3
            if alpha > 0:
                star_color = (*color, min(255, alpha))
                draw.ellipse([x - i, y - i, x + i, y + i], fill=star_color)

        # رسم النجمة
        if len(star_points) > 2:
            draw.polygon(star_points, fill=(*color, 200 + alpha_boost))

            # نواة مضيئة
            draw.ellipse([x - 2, y - 2, x + 2, y + 2],
                        fill=(255, 255, 255, 180 + alpha_boost))

    def draw_diamond_sparkle(self, draw, x, y, size, color, alpha_boost):
        """رسم تلألؤ الماس"""
        # شكل ماسي
        diamond_points = [
            (x, y - size),      # أعلى
            (x + size//2, y),   # يمين
            (x, y + size),      # أسفل
            (x - size//2, y),   # يسار
        ]

        # رسم هالة الماس
        for i in range(size * 3, 0, -1):
            alpha = 60 + alpha_boost - i * 2
            if alpha > 0:
                diamond_color = (*color, min(255, alpha))
                draw.ellipse([x - i, y - i, x + i, y + i], fill=diamond_color)

        # رسم الماس
        draw.polygon(diamond_points, fill=(*color, 220 + alpha_boost))

        # خطوط تلألؤ
        for angle in range(0, 360, 45):
            sparkle_length = size * 2
            end_x = x + sparkle_length * math.cos(math.radians(angle))
            end_y = y + sparkle_length * math.sin(math.radians(angle))

            draw.line([(x, y), (end_x, end_y)],
                     fill=(*color, 150 + alpha_boost), width=2)

    def draw_emerald_glow(self, draw, x, y, size, color, alpha_boost):
        """رسم توهج الزمرد"""
        # توهج دائري متدرج
        for i in range(size * 2, 0, -1):
            alpha = 70 + alpha_boost - i * 3
            if alpha > 0:
                emerald_color = (*color, min(255, alpha))
                draw.ellipse([x - i, y - i, x + i, y + i], fill=emerald_color)

        # نواة الزمرد
        draw.ellipse([x - size//2, y - size//2, x + size//2, y + size//2],
                    fill=(*color, 200 + alpha_boost))

        # نقطة مضيئة
        draw.ellipse([x - 1, y - 1, x + 1, y + 1],
                    fill=(255, 255, 255, 160 + alpha_boost))

    def draw_pearl_shimmer_particle(self, draw, x, y, size, color, alpha_boost):
        """رسم جسيمة تلألؤ اللؤلؤ"""
        # تلألؤ لؤلؤي ناعم
        for i in range(size, 0, -1):
            alpha = 90 + alpha_boost - i * 8
            if alpha > 0:
                pearl_color = (*color, min(255, alpha))
                draw.ellipse([x - i, y - i, x + i, y + i], fill=pearl_color)

        # نواة لؤلؤية
        draw.ellipse([x - size//3, y - size//3, x + size//3, y + size//3],
                    fill=(*color, 180 + alpha_boost))

    def create_ultimate_invitation_card(self, output_path):
        """إنشاء دعوة التخرج الأسطورية"""
        print("🎊 بدء إنشاء دعوة التخرج الأسطورية...")

        if not self.load_and_ultimate_enhance():
            return False

        # إنشاء خلفية البطاقة الفاخرة
        print("💎 إنشاء خلفية البطاقة الفاخرة...")
        luxury_card = self.create_luxury_card_background()

        # تحضير الصورة الشخصية
        print("🖼️ تحضير الصورة الشخصية...")
        personal_img = self.prepare_personal_image()

        # دمج الصورة مع البطاقة
        result = Image.alpha_composite(luxury_card, personal_img)

        # إضافة النصوص بتأثيرات أسطورية
        print("✨ إضافة النصوص بتأثيرات أسطورية...")

        # العنوان الرئيسي
        title_text = "دعوة تخرج"
        title_layer = self.create_epic_text_effects(title_text, int(self.card_width * 0.06),
                                                   int(self.card_height * 0.15), 'title')
        result = Image.alpha_composite(result, title_layer)

        # اسم الدكتور - النص المميز
        name_text = "د.محمد صادق"
        name_layer = self.create_epic_text_effects(name_text, int(self.card_width * 0.08),
                                                  int(self.card_height * 0.65), 'highlight')
        result = Image.alpha_composite(result, name_layer)

        # النص الفرعي
        subtitle_text = "لحفل تخرج حبيب القلب"
        subtitle_layer = self.create_epic_text_effects(subtitle_text, int(self.card_width * 0.035),
                                                      int(self.card_height * 0.75))
        result = Image.alpha_composite(result, subtitle_layer)

        # التاريخ والمكان
        date_text = "موعدنا اليوم 2025/7/28 في قاعة مملكة سبأ"
        date_layer = self.create_epic_text_effects(date_text, int(self.card_width * 0.028),
                                                  int(self.card_height * 0.85))
        result = Image.alpha_composite(result, date_layer)

        # إضافة نظام الجسيمات الفاخر
        print("✨ إضافة نظام الجسيمات الفاخر...")
        luxury_particles = self.add_luxury_particle_system()
        result = Image.alpha_composite(result, luxury_particles)

        # تطبيق التحسينات النهائية
        print("🎨 تطبيق التحسينات النهائية...")
        result = self.apply_final_luxury_enhancements(result)

        # حفظ النتيجة النهائية بأعلى جودة
        final_result = result.convert('RGB')
        final_result.save(output_path, 'PNG', quality=100, optimize=True, dpi=(600, 600))

        print(f"✅ تم إنشاء دعوة التخرج الأسطورية: {output_path}")
        return True

    def prepare_personal_image(self):
        """تحضير الصورة الشخصية"""
        # تغيير حجم الصورة لتناسب البطاقة
        img_width = int(self.card_width * 0.35)
        img_height = int(self.card_height * 0.45)

        # تغيير الحجم مع الحفاظ على النسبة
        self.base_img.thumbnail((img_width, img_height), Image.Resampling.LANCZOS)

        # إنشاء طبقة للصورة
        img_layer = Image.new('RGBA', (self.card_width, self.card_height), (0, 0, 0, 0))

        # موضع الصورة (يمين البطاقة)
        img_x = int(self.card_width * 0.6)
        img_y = int(self.card_height * 0.25)

        # إضافة إطار فاخر للصورة
        frame_img = self.add_luxury_frame(self.base_img)

        # لصق الصورة
        img_layer.paste(frame_img, (img_x, img_y), frame_img)

        return img_layer

    def add_luxury_frame(self, img):
        """إضافة إطار فاخر للصورة"""
        # إنشاء إطار أكبر
        frame_padding = 30
        frame_width = img.width + frame_padding * 2
        frame_height = img.height + frame_padding * 2

        frame = Image.new('RGBA', (frame_width, frame_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(frame)

        # رسم إطار متعدد الطبقات
        for i in range(frame_padding, 0, -2):
            alpha = 150 + i * 3

            if i > frame_padding * 0.8:
                color = (*self.luxury_colors['royal_gold'], alpha)
            elif i > frame_padding * 0.6:
                color = (*self.luxury_colors['platinum'], alpha)
            elif i > frame_padding * 0.4:
                color = (*self.luxury_colors['diamond_white'], alpha)
            else:
                color = (*self.luxury_colors['pearl_white'], alpha)

            draw.rectangle([i, i, frame_width - i, frame_height - i],
                          outline=color, width=3)

        # لصق الصورة في المركز
        frame.paste(img, (frame_padding, frame_padding), img)

        return frame

    def apply_final_luxury_enhancements(self, img):
        """تطبيق التحسينات الفاخرة النهائية"""
        # تحسين الألوان
        enhancer = ImageEnhance.Color(img)
        img = enhancer.enhance(1.4)

        # تحسين التباين
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.3)

        # تحسين السطوع
        enhancer = ImageEnhance.Brightness(img)
        img = enhancer.enhance(1.1)

        # تطبيق فلتر حدة
        img = img.filter(UnsharpMask(radius=2, percent=150, threshold=1))

        return img

def main():
    """الدالة الرئيسية"""
    input_image = "WhatsApp Image 2025-07-28 at 03.29.09_612d4d3e (1).png"
    output_image = "ultimate_graduation_invitation.png"

    if not os.path.exists(input_image):
        print(f"❌ لم يتم العثور على الصورة: {input_image}")
        return

    # إنشاء مصمم الدعوة الأسطورية
    designer = UltimateInvitationDesigner(input_image)

    # إنشاء دعوة التخرج الأسطورية
    success = designer.create_ultimate_invitation_card(output_image)

    if success:
        print("\n🏆 تم إنشاء دعوة التخرج الأسطورية!")
        print(f"📁 الملف: {output_image}")
        print("🎊 المميزات الأسطورية:")
        print("   💎 تصميم بطاقة فاخرة بالنسبة الذهبية")
        print("   🌌 خلفية كونية فاخرة متعددة الطبقات")
        print("   ✨ تأثيرات نص ملكية وماسية")
        print("   🎭 إطار فاخر للصورة الشخصية")
        print("   💫 نظام جسيمات فاخر متنوع")
        print("   🏰 زخارف إسلامية ملكية")
        print("   💍 أحجار كريمة عائمة")
        print("   🌟 تأثيرات اللؤلؤ والماس")
        print("   🎨 تحسينات الذكاء الاصطناعي")
        print("   🖨️ جودة طباعة فائقة (600 DPI)")
        print("\n🎉 هذه أجمل دعوة تخرج في التاريخ!")

if __name__ == "__main__":
    main()
