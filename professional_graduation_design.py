#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎨 تحفة فنية رقمية احترافية لتخرج د.محمد صادق
Professional Digital Art Masterpiece - Photoshop Level Quality
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from PIL.ImageFilter import GaussianBlur
import arabic_reshaper
from bidi.algorithm import get_display
import math
import os
import sys

class ProfessionalDesigner:
    def __init__(self, image_path):
        self.image_path = image_path
        self.base_img = None
        self.width = 0
        self.height = 0
        
    def load_image(self):
        """تحميل وتحضير الصورة بجودة عالية"""
        try:
            self.base_img = Image.open(self.image_path).convert('RGBA')
            self.width, self.height = self.base_img.size
            
            # تحسين الجودة والوضوح
            enhancer = ImageEnhance.Sharpness(self.base_img)
            self.base_img = enhancer.enhance(1.5)
            
            enhancer = ImageEnhance.Contrast(self.base_img)
            self.base_img = enhancer.enhance(1.3)
            
            print(f"✅ تم تحميل الصورة بنجاح: {self.width}x{self.height}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل الصورة: {e}")
            return False
    
    def create_cinematic_lighting(self):
        """إنشاء إضاءة سينمائية متقدمة"""
        lighting = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        
        # إنشاء مصفوفة للإضاءة المتدرجة
        light_array = np.zeros((self.height, self.width, 4), dtype=np.uint8)
        
        center_x, center_y = self.width // 2, self.height // 3
        
        for y in range(self.height):
            for x in range(self.width):
                # حساب المسافة من مركز الإضاءة
                distance = math.sqrt((x - center_x)**2 + (y - center_y)**2)
                max_distance = math.sqrt(self.width**2 + self.height**2)
                
                # إنشاء تأثير الإضاءة المتدرجة
                intensity = max(0, 1 - (distance / max_distance) * 1.5)
                
                # ألوان الإضاءة الذهبية الدافئة
                if intensity > 0.7:
                    # منطقة الضوء المركزي - ذهبي لامع
                    r, g, b = 255, 235, 120
                    alpha = int(intensity * 80)
                elif intensity > 0.4:
                    # منطقة متوسطة - ذهبي دافئ
                    r, g, b = 255, 200, 80
                    alpha = int(intensity * 60)
                elif intensity > 0.2:
                    # منطقة خارجية - برتقالي ذهبي
                    r, g, b = 200, 150, 60
                    alpha = int(intensity * 40)
                else:
                    # منطقة الظلال - أزرق داكن
                    r, g, b = 20, 30, 80
                    alpha = int(intensity * 30)
                
                light_array[y, x] = [r, g, b, alpha]
        
        lighting = Image.fromarray(light_array, 'RGBA')
        
        # إضافة تأثير الضبابية للنعومة
        lighting = lighting.filter(GaussianBlur(radius=15))
        
        return lighting
    
    def create_luxury_background(self):
        """إنشاء خلفية فاخرة متعددة الطبقات"""
        bg = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 255))
        
        # الطبقة الأولى: تدرج أساسي فاخر
        gradient = self.create_royal_gradient()
        bg = Image.alpha_composite(bg, gradient)
        
        # الطبقة الثانية: نسيج ذهبي
        texture = self.create_gold_texture()
        bg = Image.alpha_composite(bg, texture)
        
        # الطبقة الثالثة: أنماط هندسية إسلامية
        patterns = self.create_islamic_patterns()
        bg = Image.alpha_composite(bg, patterns)
        
        # الطبقة الرابعة: تأثيرات الضوء والظل
        lighting = self.create_cinematic_lighting()
        bg = Image.alpha_composite(bg, lighting)
        
        return bg
    
    def create_royal_gradient(self):
        """إنشاء تدرج ملكي فاخر"""
        gradient = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        gradient_array = np.zeros((self.height, self.width, 4), dtype=np.uint8)
        
        # نقاط التدرج الملكي
        colors = [
            (15, 15, 40, 255),      # أزرق ملكي عميق
            (25, 25, 70, 240),      # أزرق كحلي
            (40, 35, 90, 220),      # أزرق متوسط
            (120, 80, 20, 200),     # بني ذهبي
            (180, 140, 30, 180),    # ذهبي داكن
            (220, 180, 50, 160),    # ذهبي متوسط
            (255, 220, 80, 140),    # ذهبي لامع
            (255, 240, 200, 120),   # كريمي ذهبي
        ]
        
        for y in range(self.height):
            ratio = y / self.height
            
            # تحديد اللون بناءً على الموضع
            if ratio < 0.15:
                color_idx = 0
                local_ratio = ratio / 0.15
            elif ratio < 0.35:
                color_idx = 1
                local_ratio = (ratio - 0.15) / 0.2
            elif ratio < 0.55:
                color_idx = 2
                local_ratio = (ratio - 0.35) / 0.2
            elif ratio < 0.7:
                color_idx = 3
                local_ratio = (ratio - 0.55) / 0.15
            elif ratio < 0.85:
                color_idx = 4
                local_ratio = (ratio - 0.7) / 0.15
            else:
                color_idx = 5
                local_ratio = (ratio - 0.85) / 0.15
            
            if color_idx < len(colors) - 1:
                c1 = colors[color_idx]
                c2 = colors[color_idx + 1]
                
                r = int(c1[0] + (c2[0] - c1[0]) * local_ratio)
                g = int(c1[1] + (c2[1] - c1[1]) * local_ratio)
                b = int(c1[2] + (c2[2] - c1[2]) * local_ratio)
                a = int(c1[3] + (c2[3] - c1[3]) * local_ratio)
            else:
                r, g, b, a = colors[-1]
            
            gradient_array[y, :] = [r, g, b, a]
        
        return Image.fromarray(gradient_array, 'RGBA')
    
    def create_gold_texture(self):
        """إنشاء نسيج ذهبي متقدم"""
        texture = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        texture_array = np.zeros((self.height, self.width, 4), dtype=np.uint8)
        
        # إنشاء نسيج ذهبي باستخدام الضوضاء
        for y in range(self.height):
            for x in range(self.width):
                # إنشاء نمط ضوضاء متعدد الطبقات
                noise1 = math.sin(x * 0.01) * math.cos(y * 0.01)
                noise2 = math.sin(x * 0.02 + 100) * math.cos(y * 0.02 + 100)
                noise3 = math.sin(x * 0.005) * math.cos(y * 0.005)
                
                combined_noise = (noise1 + noise2 * 0.5 + noise3 * 0.3) / 1.8
                
                # تحويل الضوضاء إلى قيم ذهبية
                if combined_noise > 0.3:
                    intensity = min(1.0, (combined_noise + 1) / 2)
                    r = int(255 * intensity)
                    g = int(215 * intensity)
                    b = int(50 * intensity)
                    a = int(30 * intensity)
                    texture_array[y, x] = [r, g, b, a]
        
        texture = Image.fromarray(texture_array, 'RGBA')
        return texture.filter(GaussianBlur(radius=2))
    
    def create_islamic_patterns(self):
        """إنشاء أنماط إسلامية معقدة"""
        patterns = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(patterns)
        
        # رسم أنماط هندسية إسلامية في الزوايا
        self.draw_corner_patterns(draw)
        
        # رسم زخارف مركزية
        self.draw_central_ornaments(draw)
        
        # رسم حدود زخرفية
        self.draw_decorative_borders(draw)
        
        return patterns
    
    def draw_corner_patterns(self, draw):
        """رسم أنماط الزوايا الإسلامية"""
        corner_size = min(self.width, self.height) // 6
        
        # الزاوية العلوية اليمنى
        for i in range(12):
            radius = corner_size - i * 8
            if radius > 0:
                alpha = 100 - i * 7
                color = (255, 215, 0, alpha)
                
                # رسم أقواس متداخلة
                draw.arc([self.width - corner_size - 30, 30, 
                         self.width - 30, corner_size + 30], 
                        start=180, end=270, fill=color, width=3)
                
                # رسم أنماط داخلية
                if i % 3 == 0:
                    inner_radius = radius // 2
                    draw.arc([self.width - corner_size - 30 + inner_radius//2, 
                             30 + inner_radius//2,
                             self.width - 30 - inner_radius//2, 
                             corner_size + 30 - inner_radius//2], 
                            start=180, end=270, fill=color, width=2)
        
        # الزاوية السفلية اليسرى (مماثلة)
        for i in range(12):
            radius = corner_size - i * 8
            if radius > 0:
                alpha = 100 - i * 7
                color = (255, 215, 0, alpha)
                
                draw.arc([30, self.height - corner_size - 30, 
                         corner_size + 30, self.height - 30], 
                        start=0, end=90, fill=color, width=3)
                
                if i % 3 == 0:
                    inner_radius = radius // 2
                    draw.arc([30 + inner_radius//2, 
                             self.height - corner_size - 30 + inner_radius//2,
                             corner_size + 30 - inner_radius//2, 
                             self.height - 30 - inner_radius//2], 
                            start=0, end=90, fill=color, width=2)
    
    def draw_central_ornaments(self, draw):
        """رسم الزخارف المركزية"""
        center_x, center_y = self.width // 2, self.height // 2
        
        # رسم نجمة إسلامية مركزية
        for size in range(80, 20, -10):
            alpha = 120 - (80 - size) * 2
            self.draw_islamic_star(draw, center_x, center_y - self.height//4, 
                                 size, (255, 215, 0, alpha))
    
    def draw_decorative_borders(self, draw):
        """رسم الحدود الزخرفية"""
        # حدود علوية
        for i in range(5):
            y = 20 + i * 3
            alpha = 150 - i * 25
            draw.line([(self.width * 0.2, y), (self.width * 0.8, y)], 
                     fill=(255, 215, 0, alpha), width=2)
        
        # حدود سفلية
        for i in range(5):
            y = self.height - 20 - i * 3
            alpha = 150 - i * 25
            draw.line([(self.width * 0.15, y), (self.width * 0.85, y)], 
                     fill=(255, 215, 0, alpha), width=2)
    
    def draw_islamic_star(self, draw, x, y, size, color):
        """رسم نجمة إسلامية ثمانية"""
        points = []
        for i in range(16):
            angle = i * 22.5 * math.pi / 180
            if i % 2 == 0:
                radius = size
            else:
                radius = size * 0.4
            
            px = x + radius * math.cos(angle)
            py = y + radius * math.sin(angle)
            points.append((px, py))
        
        draw.polygon(points, fill=color)

    def create_3d_text_effect(self, text, font_size, position, is_highlight=False):
        """إنشاء تأثير نص ثلاثي الأبعاد متقدم"""
        # إنشاء طبقة للنص
        text_layer = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(text_layer)

        # تحضير النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        display_text = get_display(reshaped_text)

        # تحميل الخط
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()

        # حساب موضع النص
        bbox = draw.textbbox((0, 0), display_text, font=font)
        text_width = bbox[2] - bbox[0]
        text_x = (self.width - text_width) // 2
        text_y = position

        if is_highlight:
            # تأثير خاص للنص المميز (اسم الدكتور)
            self.create_highlight_text_effect(draw, display_text, font, text_x, text_y)
        else:
            # تأثير عادي للنص
            self.create_normal_text_effect(draw, display_text, font, text_x, text_y)

        return text_layer

    def create_highlight_text_effect(self, draw, text, font, x, y):
        """تأثير النص المميز - اسم الدكتور"""
        # إنشاء هالة ضوئية خلف النص
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # رسم هالة متدرجة
        for i in range(25, 0, -1):
            alpha = 15 + i * 2
            glow_color = (255, 215, 0, alpha)

            # رسم دائرة مضيئة خلف النص
            draw.ellipse([x - i*4, y - i*3, x + text_width + i*4, y + text_height + i*3],
                        fill=glow_color)

        # رسم الظل ثلاثي الأبعاد
        shadow_layers = [
            (8, 8, (0, 0, 0, 100)),
            (6, 6, (20, 20, 40, 120)),
            (4, 4, (40, 40, 80, 140)),
            (2, 2, (60, 60, 120, 160)),
        ]

        for dx, dy, color in shadow_layers:
            draw.text((x + dx, y + dy), text, font=font, fill=color)

        # رسم التوهج الذهبي
        for offset in range(15, 0, -1):
            alpha = 30 + offset * 4
            glow_color = (255, 215, 0, alpha)

            for dx in range(-offset, offset + 1, 2):
                for dy in range(-offset, offset + 1, 2):
                    if dx*dx + dy*dy <= offset*offset:
                        draw.text((x + dx, y + dy), text, font=font, fill=glow_color)

        # النص الأساسي بتدرج ذهبي
        # إنشاء تدرج ذهبي للنص
        gradient_colors = [
            (255, 240, 120),  # ذهبي فاتح
            (255, 215, 0),    # ذهبي كلاسيكي
            (218, 165, 32),   # ذهبي داكن
            (184, 134, 11),   # ذهبي عميق
        ]

        # رسم النص بتدرج
        for i, color in enumerate(gradient_colors):
            offset = i * 2
            draw.text((x - offset, y - offset), text, font=font,
                     fill=(*color, 255), stroke_width=2, stroke_fill=(25, 25, 60, 255))

    def create_normal_text_effect(self, draw, text, font, x, y):
        """تأثير النص العادي"""
        # رسم الظل
        shadow_layers = [
            (4, 4, (0, 0, 0, 120)),
            (3, 3, (20, 20, 40, 140)),
            (2, 2, (40, 40, 80, 160)),
        ]

        for dx, dy, color in shadow_layers:
            draw.text((x + dx, y + dy), text, font=font, fill=color)

        # رسم التوهج
        for offset in range(8, 0, -1):
            alpha = 25 + offset * 5
            glow_color = (200, 200, 255, alpha)

            for dx in range(-offset, offset + 1, 2):
                for dy in range(-offset, offset + 1, 2):
                    if dx*dx + dy*dy <= offset*offset:
                        draw.text((x + dx, y + dy), text, font=font, fill=glow_color)

        # النص الأساسي
        draw.text((x, y), text, font=font, fill=(255, 255, 255, 255),
                 stroke_width=2, stroke_fill=(100, 150, 255, 200))

    def add_particle_effects(self):
        """إضافة تأثيرات الجسيمات المتلألئة"""
        particles = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(particles)

        # إنشاء جسيمات ذهبية متلألئة
        particle_positions = []

        # توزيع الجسيمات بشكل فني
        for i in range(150):
            x = np.random.randint(0, self.width)
            y = np.random.randint(0, self.height)
            size = np.random.randint(2, 8)
            alpha = np.random.randint(50, 200)

            # تركيز أكثر حول مناطق النص
            if self.height * 0.6 < y < self.height * 0.9:
                alpha = min(255, alpha + 50)
                size += 2

            particle_positions.append((x, y, size, alpha))

        # رسم الجسيمات
        for x, y, size, alpha in particle_positions:
            # جسيمة ذهبية مع توهج
            for i in range(size, 0, -1):
                particle_alpha = alpha // (size - i + 1)
                color = (255, 215, 0, particle_alpha)
                draw.ellipse([x - i, y - i, x + i, y + i], fill=color)

            # نقطة مركزية لامعة
            draw.ellipse([x - 1, y - 1, x + 1, y + 1], fill=(255, 255, 255, alpha))

        return particles

    def add_lens_flare_effects(self):
        """إضافة تأثيرات وهج العدسة"""
        flare = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(flare)

        # مواضع مصادر الضوء
        light_sources = [
            (self.width * 0.2, self.height * 0.3),
            (self.width * 0.8, self.height * 0.2),
            (self.width * 0.5, self.height * 0.1),
        ]

        for lx, ly in light_sources:
            # رسم وهج العدسة
            for i in range(20, 0, -1):
                alpha = 15 + i
                size = i * 3
                color = (255, 255, 200, alpha)

                draw.ellipse([lx - size, ly - size, lx + size, ly + size], fill=color)

            # نقطة ضوء مركزية
            draw.ellipse([lx - 5, ly - 5, lx + 5, ly + 5], fill=(255, 255, 255, 200))

        return flare

    def create_masterpiece(self, output_path):
        """إنشاء التحفة الفنية الكاملة"""
        print("🎨 بدء إنشاء التحفة الفنية المحترفة...")

        if not self.load_image():
            return False

        # إنشاء الخلفية الفاخرة
        print("🌟 إنشاء الخلفية الفاخرة متعددة الطبقات...")
        luxury_bg = self.create_luxury_background()

        # دمج الصورة الأساسية مع الخلفية
        result = Image.alpha_composite(luxury_bg, self.base_img)

        # إضافة النصوص بتأثيرات ثلاثية الأبعاد
        print("✨ إضافة النصوص بتأثيرات ثلاثية الأبعاد...")

        # النص الرئيسي
        main_text = "موعدنا اليوم 2025/7/28 في قاعة مملكة سبأ"
        main_text_layer = self.create_3d_text_effect(main_text, int(self.width * 0.025),
                                                   int(self.height * 0.72))
        result = Image.alpha_composite(result, main_text_layer)

        # النص المميز (اسم الدكتور)
        highlight_text = "د.محمد صادق"
        highlight_layer = self.create_3d_text_effect(highlight_text, int(self.width * 0.045),
                                                   int(self.height * 0.78), is_highlight=True)
        result = Image.alpha_composite(result, highlight_layer)

        # النص الفرعي
        subtitle_text = "لحفل تخرج حبيب القلب"
        subtitle_layer = self.create_3d_text_effect(subtitle_text, int(self.width * 0.02),
                                                  int(self.height * 0.86))
        result = Image.alpha_composite(result, subtitle_layer)

        # إضافة تأثيرات الجسيمات
        print("💫 إضافة تأثيرات الجسيمات المتلألئة...")
        particles = self.add_particle_effects()
        result = Image.alpha_composite(result, particles)

        # إضافة تأثيرات وهج العدسة
        print("🔆 إضافة تأثيرات وهج العدسة...")
        lens_flare = self.add_lens_flare_effects()
        result = Image.alpha_composite(result, lens_flare)

        # تطبيق تحسينات نهائية
        print("🎭 تطبيق التحسينات النهائية...")

        # تحسين الألوان
        enhancer = ImageEnhance.Color(result)
        result = enhancer.enhance(1.3)

        # تحسين التباين
        enhancer = ImageEnhance.Contrast(result)
        result = enhancer.enhance(1.2)

        # تحسين السطوع
        enhancer = ImageEnhance.Brightness(result)
        result = enhancer.enhance(1.1)

        # حفظ النتيجة النهائية
        final_result = result.convert('RGB')
        final_result.save(output_path, 'PNG', quality=100, optimize=True)

        print(f"✅ تم إنشاء التحفة الفنية المحترفة: {output_path}")
        return True

def main():
    """الدالة الرئيسية"""
    input_image = "WhatsApp Image 2025-07-28 at 03.29.09_612d4d3e (1).png"
    output_image = "professional_graduation_masterpiece.png"

    if not os.path.exists(input_image):
        print(f"❌ لم يتم العثور على الصورة: {input_image}")
        return

    # إنشاء المصمم المحترف
    designer = ProfessionalDesigner(input_image)

    # إنشاء التحفة الفنية
    success = designer.create_masterpiece(output_image)

    if success:
        print("\n🎉 تم إنشاء تحفة فنية محترفة بمستوى Photoshop!")
        print(f"📁 الملف: {output_image}")
        print("🌟 المميزات المحترفة:")
        print("   • إضاءة سينمائية متقدمة")
        print("   • تأثيرات ثلاثية الأبعاد للنصوص")
        print("   • خلفية فاخرة متعددة الطبقات")
        print("   • أنماط إسلامية معقدة")
        print("   • تأثيرات الجسيمات المتلألئة")
        print("   • وهج العدسة الاحترافي")
        print("   • نسيج ذهبي متقدم")
        print("   • تدرجات ملكية فاخرة")

if __name__ == "__main__":
    main()
